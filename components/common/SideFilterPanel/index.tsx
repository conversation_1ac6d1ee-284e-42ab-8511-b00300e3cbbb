import React, { useEffect, useRef, useMemo, useState } from 'react';
import { Dimensions, StyleSheet, Modal, View, TouchableWithoutFeedback, TouchableOpacity, StatusBar, Platform, ActivityIndicator, BackHandler } from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS
} from 'react-native-reanimated';
import { CategoryItem, getCurrentUserGender } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getGenderSpecificCategories } from '@/utils/categoryUtils';
import { getUserProfile } from '@/methods/users';
import { getStandardizedCategories, formatCategoriesForDisplay } from '@/utils/standardCategories';
import { getClothes, SUB_CATEGORIES, MAIN_CATEGORIES } from '@/methods/cloths';

// Get screen dimensions
const { width } = Dimensions.get('window');
const DRAWER_WIDTH = width * 0.7; // 70% of screen width to match design

interface SideFilterPanelProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectCategory: (categoryId: string, categoryName: string) => void;
  selectedCategory: string;
  activeMainCategory: string; // Main category like "Clothes", "Accessories", etc.
}

export default function SideFilterPanel({
  isVisible,
  onClose,
  onSelectCategory,
  selectedCategory,
  activeMainCategory
}: SideFilterPanelProps) {
  // Animation value for the drawer position
  const translateX = useSharedValue(DRAWER_WIDTH);

  // Track if animation is complete for cleanup
  const animationComplete = useRef(true);

  // State for gender-specific categories
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Create a custom "View All" category
  const viewAllCategory: CategoryItem = { id: 'all', name: 'View All' };

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Fetch all items to determine which categories have items
  const { data: allItemsData } = getClothes({});

  // Helper function to get display category name from an item (similar to closet component)
  const getCategoryDisplayName = (item: any): string => {
    // First priority: Use the categoryName field that we now populate in processClothesData
    if (item.categoryName) {
      return item.categoryName;
    }

    // Use category.mainCategory field (current backend schema)
    if (item.category?.mainCategory) {
      return item.category.mainCategory;
    }

    // Fallback: Try legacy category.name field
    if (item.category?.name) {
      return item.category.name;
    }

    // Try mainCategory field directly on item (if it exists)
    if (item.mainCategory) {
      return item.mainCategory;
    }

    // If we have an itemCategoryId, try to map it using backend categories first
    if (item.itemCategoryId) {
      // Try to find the category in the backend categories map
      const categoryIdToNameMap = new Map<string, string>();

      // This will be populated when getUsedCategories is called with backend categories
      // For now, use comprehensive mapping as fallback

      // Enhanced category mapping that covers more possibilities
      const comprehensiveCategoryMap: {[key: string]: string} = {
        // Direct matches
        'tops': 'Tops',
        'bottoms': 'Bottoms',
        'shoes': 'Shoes',
        'dresses': 'Dresses',
        'accessories': 'Accessories',
        'outerwear': 'Outerwear',
        'loungewear': 'Loungewear',
        'activewear': 'Activewear',
        'swimwear': 'Swimwear',
        'underwear': 'Underwear',
        'sleepwear': 'Sleepwear',
        'jewelry': 'Jewelry',
        'bags': 'Bags',
        'tech': 'Tech',
        'others': 'Others',
        'matching sets': 'Matching Sets',
        'sweaters': 'Sweaters',
        'coats': 'Coats',
        'jackets': 'Jackets',
        'pants': 'Pants',
        'jeans': 'Jeans',
        'skirts': 'Skirts',
        'shorts': 'Shorts',
        'blouses': 'Blouses',
        'shirts': 'Shirts',
        't-shirts': 'T-Shirts',
        'tank tops': 'Tank Tops',
        'cardigans': 'Cardigans',
        'hoodies': 'Hoodies',
        'sweatshirts': 'Sweatshirts',
        'blazers': 'Blazers',
        'vests': 'Vests',
        'rompers': 'Rompers',
        'jumpsuits': 'Jumpsuits',
        'leggings': 'Leggings',
        'tights': 'Tights',
        'socks': 'Socks',
        'belts': 'Belts',
        'hats': 'Hats',
        'scarves': 'Scarves',
        'gloves': 'Gloves',
        'sunglasses': 'Sunglasses',
        'watches': 'Watches',
        'necklaces': 'Necklaces',
        'earrings': 'Earrings',
        'bracelets': 'Bracelets',
        'rings': 'Rings',
        // Common backend category patterns
        'sxw94qd2r8iuhmwpp': 'Tops',
        'bbonwezchd pqwcn6u': 'Bottoms',
        'txnwwn3k83q3je3lm': 'Shoes',
        'znpdehjkbf78onlrd': 'Tech',
        'ogcjqfavcm2qhfte4': 'Accessories'
      };

      const itemCategoryLower = item.itemCategoryId.toLowerCase();

      // First try exact match with the ID
      if (comprehensiveCategoryMap[itemCategoryLower]) {
        return comprehensiveCategoryMap[itemCategoryLower];
      }

      // Then try partial matches
      for (const [key, value] of Object.entries(comprehensiveCategoryMap)) {
        if (itemCategoryLower.includes(key) || key.includes(itemCategoryLower)) {
          return value;
        }
      }

      // If no match found, return a more user-friendly fallback
      return 'Other';
    }

    // Fallback to 'Uncategorized' if no category information is available
    return 'Uncategorized';
  };

  // Helper function to extract unique categories from items
  const getUsedCategories = (items: any[], backendCategories: CategoryItem[] = []): Set<string> => {
    console.log('\n🔍 SIDE FILTER PANEL - ANALYZING USED CATEGORIES 🔍');
    const usedCategories = new Set<string>();

    if (!items || !Array.isArray(items)) {
      console.log('❌ No items or items is not an array');
      return usedCategories;
    }

    console.log('📊 Total items to analyze:', items.length);

    // Create a map of backend category IDs to names for quick lookup
    const categoryIdToNameMap = new Map<string, string>();
    backendCategories.forEach(category => {
      if (category.id && category.name) {
        categoryIdToNameMap.set(category.id.toLowerCase(), category.name);
      }
    });

    items.forEach((item, index) => {
      console.log(`Item ${index + 1}:`, {
        id: item._id,
        name: item.name,
        category: item.category,
        categoryName: item.categoryName,
        itemCategoryId: item.itemCategoryId
      });

      // Get the display category name using our comprehensive mapping
      const displayCategoryName = getCategoryDisplayName(item);
      const categoryNameLower = displayCategoryName.toLowerCase().trim();

      usedCategories.add(categoryNameLower);
      console.log(`✅ Added category: "${categoryNameLower}" (display: "${displayCategoryName}")`);
    });

    console.log('📋 Final used categories:', Array.from(usedCategories));
    return usedCategories;
  };

  // Load categories based on active main category
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        console.log('🔍 SideFilterPanel - Loading categories for main category:', activeMainCategory);

        // Get subcategories for the active main category
        let subcategories: string[] = [];

        if (activeMainCategory === "Outfits") {
          // For outfits, show outfit-specific categories
          subcategories = ["All", "Work Wear", "Casual Wear", "Evening Wear", "Formal Wear"];
        } else {
          // Get subcategories from the mapping
          const mainCategoryKey = activeMainCategory as keyof typeof SUB_CATEGORIES;
          console.log('🔍 Looking up subcategories for:', activeMainCategory, 'Key:', mainCategoryKey);
          console.log('🔍 Available SUB_CATEGORIES keys:', Object.keys(SUB_CATEGORIES));

          if (SUB_CATEGORIES[mainCategoryKey]) {
            subcategories = ["All", ...SUB_CATEGORIES[mainCategoryKey]];
            console.log('✅ Found subcategories:', subcategories);
          } else {
            // Fallback for unknown categories
            subcategories = ["All"];
            console.log('⚠️ No subcategories found, using fallback:', subcategories);
          }
        }

        console.log('📋 Subcategories for', activeMainCategory, ':', subcategories);

        // Convert to CategoryItem format
        const categoryItems: CategoryItem[] = subcategories.map((subcat, index) => ({
          id: subcat === "All" ? "all" : `${activeMainCategory.toLowerCase()}-${subcat.toLowerCase().replace(/\s+/g, '-')}`,
          name: subcat
        }));

        // For non-Outfit categories, filter based on items that actually exist
        if (activeMainCategory !== "Outfits") {
          console.log('🔍 Filtering categories based on existing items...');

          // Get categories that have items in them
          const usedCategories = getUsedCategories(allItemsData?.items || [], []);

          // Filter categories to show only those that have items (except "All")
          const filteredCategories = categoryItems.filter((category) => {
            // Always include "All"
            if (category.name === "All") {
              return true;
            }

            // Check if this category has items
            const categoryNameLower = category.name.toLowerCase().trim();
            const hasItems = usedCategories.has(categoryNameLower);
            console.log(`${hasItems ? '✅' : '❌'} Category "${category.name}" ${hasItems ? 'has' : 'has no'} items`);
            return hasItems;
          });

          setCategories(filteredCategories);
        } else {
          // For Outfits, show all categories
          setCategories(categoryItems);
        }

      } catch (error) {
        console.error('Error loading categories:', error);
        // Fallback to basic categories
        setCategories([
          { id: 'all', name: 'All' }
        ]);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, [activeMainCategory, allItemsData]); // Re-run when main category or items data changes

  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Update animation when visibility changes
  useEffect(() => {
    if (isVisible) {
      // Opening the drawer
      animationComplete.current = false;
      translateX.value = withTiming(
        0,
        {
          duration: 250, // Faster animation to match other components
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }
      );
    } else {
      // Closing the drawer
      translateX.value = withTiming(
        DRAWER_WIDTH,
        {
          duration: 250,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        },
        (finished) => {
          if (finished) {
            runOnJS(completeAnimation)();
          }
        }
      );
    }
  }, [isVisible]);

  // Function to mark animation as complete
  const completeAnimation = () => {
    animationComplete.current = true;
  };

  // Create animated style for the drawer
  const drawerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  // Handle category selection
  const handleCategorySelect = (category: CategoryItem) => {
    onSelectCategory(category.id, category.name);
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>

        <Animated.View style={[styles.drawer, drawerAnimatedStyle]}>
          <View style={styles.header}>
            <TouchableWithoutFeedback onPress={onClose}>
              <View style={styles.closeButton}>
                <ChevronLeft size={24} color="#0E7E61" />
              </View>
            </TouchableWithoutFeedback>
            <Animated.Text style={styles.headerTitle}>
              {activeMainCategory === 'Outfits' ? 'Outfits' : 'Categories'}
            </Animated.Text>
          </View>

          <Animated.ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
          >
            {activeMainCategory !== 'Outfits' ? (
              isLoadingCategories ? (
                // Show loading indicator while categories are loading
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#0E7E61" />
                  <Animated.Text style={styles.loadingText}>
                    Loading categories...
                  </Animated.Text>
                </View>
              ) : (
                // Show categories for Clothes tab
                categories.map((category) => {
                  // Check if this category is selected (handle both name and id matching)
                  // Add null/undefined checks to prevent errors
                  const isSelected =
                    selectedCategory === category.name ||
                    (category.id === 'all' && (selectedCategory === 'All' || selectedCategory === 'View All Clothing')) ||
                    (typeof selectedCategory === 'string' &&
                     typeof category.name === 'string' &&
                     selectedCategory.toLowerCase() === category.name.toLowerCase());

                  return (
                    <TouchableOpacity
                      key={category.id}
                      onPress={() => handleCategorySelect(category)}
                      activeOpacity={0.7}
                    >
                      <View style={[
                        styles.categoryItem,
                        isSelected && styles.selectedCategoryItem
                      ]}>
                        <Animated.Text style={[
                          styles.categoryText,
                          isSelected && styles.selectedCategoryText
                        ]}>
                          {category.name}
                        </Animated.Text>
                      </View>
                    </TouchableOpacity>
                  );
                })
              )
            ) : (
              // Show message for Outfits tab
              <View style={styles.emptyStateContainer}>
                <Animated.Text style={styles.emptyStateText}>
                  No outfit categories available yet.
                </Animated.Text>
              </View>
            )}
          </Animated.ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
}

// Get the status bar height
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)', // Increased opacity to backdrop
  },
  drawer: {
    width: DRAWER_WIDTH,
    height: '100%',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    paddingTop: STATUSBAR_HEIGHT, // Add padding for status bar
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    position: 'relative',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  headerTitle: {
    fontFamily: 'MuktaVaani',
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    color: '#1C1C1C',
  },
  closeButton: {
    position: 'absolute',
    left: 0,
    padding: 10,
    zIndex: 1,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  categoryItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: 'transparent',
  },
  selectedCategoryItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#0E7E61',
  },
  categoryText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 16,
    color: '#1C1C1C',
    fontWeight: 'normal',
  },
  selectedCategoryText: {
    color: '#0E7E61',
    fontWeight: '600',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyStateText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 16,
    color: '#767676',
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  loadingText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 14,
    color: '#666',
    marginTop: 10,
  },
});
