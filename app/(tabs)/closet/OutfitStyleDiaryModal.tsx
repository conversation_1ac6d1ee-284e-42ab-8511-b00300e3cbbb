import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
} from 'react-native';
import Modal from 'react-native-modal';
import { makeImageFromView } from '@shopify/react-native-skia';

// Import Style Diary components
import { Canvas } from '@/components/Packinglist/components/canvas';
import { DraggableResizeableItem } from '@/components/Packinglist/components/dragableItem';
import ZIndexControls from '@/components/Packinglist/components/zIndexControl';
import ItemSelector from '@/components/ItemSelector';
import Button from '@/components/common/Button';
import LoadingOverlay from '@/components/LoadingOverlay';

// Import API methods
import { getClothes, UploadImage } from '@/methods/cloths';
import { createOutfit, updateOutfit } from '@/methods/outfits';

interface OutfitStyleDiaryModalProps {
  isVisible: boolean;
  onClose: () => void;
  editMode?: boolean;
  existingOutfit?: any;
}

interface Item {
  id: string;
  name: string;
  source: string;
  type: string;
  category: string;
}

interface CanvasItem {
  id: string;
  source: string;
  x: number;
  y: number;
  width: number;
  height: number;
  type: string;
  zIndex: number;
  itemId?: string; // Reference to the original closet item
}

export const OutfitStyleDiaryModal = ({
  isVisible,
  onClose,
  editMode = false,
  existingOutfit,
}: OutfitStyleDiaryModalProps) => {
  console.log('🎨 OutfitStyleDiaryModal - Render called');
  console.log('🎨 OutfitStyleDiaryModal - editMode:', editMode);
  console.log('🎨 OutfitStyleDiaryModal - existingOutfit:', existingOutfit);

  // Canvas state
  const [canvasItems, setCanvasItems] = useState<CanvasItem[]>([]);
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

  // Form state
  const [outfitName, setOutfitName] = useState('');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());

  // Refs
  const canvasRef = useRef<View>(null);

  // API hooks
  const { data: clothesData } = getClothes({});
  const { mutate: uploadImageMutation, isSuccess: isImageUploaded, data: imageData } = UploadImage();
  const { mutate: createOutfitMutation } = createOutfit();
  const { mutate: updateOutfitMutation } = updateOutfit();

  const clothes = clothesData?.data || [];

  // Convert clothes to items format for ItemSelector
  const availableItems: Item[] = clothes.map((item: any) => ({
    id: item._id,
    name: item.name,
    source: item.imageUrl,
    type: item.category?.mainCategory || 'clothing',
    category: item.category?.mainCategory || 'Other',
  }));

  // Get unique categories and filter out empty ones
  const categories = Array.from(
    new Set(
      availableItems
        .map(item => item.category)
        .filter(category => category && category !== 'All')
    )
  ).sort();

  // Populate form and canvas with existing outfit data
  useEffect(() => {
    if (editMode && existingOutfit && isVisible) {
      console.log('🎨 OutfitStyleDiaryModal - Populating with existing outfit data');
      
      setOutfitName(existingOutfit.name || '');
      setLocation(existingOutfit.location || 'London, United Kingdom');
      
      if (existingOutfit.eventDate) {
        const date = existingOutfit.eventDate instanceof Date
          ? existingOutfit.eventDate
          : new Date(existingOutfit.eventDate);
        setEventDate(date);
      }

      // Convert existing outfit items to canvas items
      if (existingOutfit.items && Array.isArray(existingOutfit.items) && existingOutfit.items.length > 0) {
        const canvasItemsFromOutfit: CanvasItem[] = existingOutfit.items.map((item: any, index: number) => {
          // Create a more distributed layout for multiple items
          const gridCols = Math.ceil(Math.sqrt(existingOutfit.items.length));
          const row = Math.floor(index / gridCols);
          const col = index % gridCols;

          return {
            id: `outfit-item-${item._id}-${index}`,
            source: item.imageUrl,
            x: 50 + (col * 120), // Space items horizontally
            y: 50 + (row * 120), // Space items vertically
            width: 100,
            height: 100,
            type: item.category?.mainCategory || 'clothing',
            zIndex: index + 1,
            itemId: item._id,
          };
        });

        setCanvasItems(canvasItemsFromOutfit);
        console.log('🎨 OutfitStyleDiaryModal - Canvas populated with', canvasItemsFromOutfit.length, 'items');
      } else if (existingOutfit.selectedItems && Array.isArray(existingOutfit.selectedItems) && existingOutfit.selectedItems.length > 0) {
        // Fallback: try to match selectedItems with loaded clothes
        console.log('🎨 OutfitStyleDiaryModal - Trying to match selectedItems with clothes');
        const outfitItems = clothes.filter((item: any) =>
          existingOutfit.selectedItems.includes(item._id)
        );

        if (outfitItems.length > 0) {
          const canvasItemsFromSelectedItems: CanvasItem[] = outfitItems.map((item: any, index: number) => {
            const gridCols = Math.ceil(Math.sqrt(outfitItems.length));
            const row = Math.floor(index / gridCols);
            const col = index % gridCols;

            return {
              id: `selected-item-${item._id}-${index}`,
              source: item.imageUrl,
              x: 50 + (col * 120),
              y: 50 + (row * 120),
              width: 100,
              height: 100,
              type: item.category?.mainCategory || 'clothing',
              zIndex: index + 1,
              itemId: item._id,
            };
          });

          setCanvasItems(canvasItemsFromSelectedItems);
          console.log('🎨 OutfitStyleDiaryModal - Canvas populated from selectedItems with', canvasItemsFromSelectedItems.length, 'items');
        }
      }
    } else if (!editMode) {
      // Reset for new outfit
      setOutfitName('');
      setLocation('London, United Kingdom');
      setEventDate(new Date());
      setCanvasItems([]);
    }
  }, [editMode, existingOutfit, isVisible]);

  // Calculate highest z-index for new items
  const highestZIndex = canvasItems.reduce(
    (max: number, item: CanvasItem) => Math.max(max, item.zIndex || 1),
    0,
  );

  // Sort canvas items by z-index for proper rendering
  const sortedCanvasItems = [...canvasItems].sort((a, b) => a.zIndex - b.zIndex);

  // Add item to canvas
  const handleAddItem = (item: Item) => {
    console.log('🎨 OutfitStyleDiaryModal - Adding item to canvas:', item.name);
    
    const newItem: CanvasItem = {
      id: `${Date.now()}-${item.id}`,
      source: item.source,
      x: 50,
      y: 50,
      width: 100,
      height: 100,
      type: item.type,
      zIndex: highestZIndex + 1,
      itemId: item.id,
    };

    setCanvasItems([...canvasItems, newItem]);
    setSelectedItemIndex(canvasItems.length);
  };

  // Update item position and size
  const handleItemChange = (index: number, changes: any) => {
    const updatedItems = [...canvasItems];
    updatedItems[index] = {
      ...updatedItems[index],
      x: changes.x !== undefined ? changes.x : updatedItems[index].x,
      y: changes.y !== undefined ? changes.y : updatedItems[index].y,
      width: changes.width !== undefined ? changes.width : updatedItems[index].width,
      height: changes.height !== undefined ? changes.height : updatedItems[index].height,
    };
    setCanvasItems(updatedItems);
  };

  // Z-index controls
  const handleBringForward = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = [...canvasItems];
      const currentItem = updatedItems[selectedItemIndex];
      const maxZIndex = Math.max(...updatedItems.map(item => item.zIndex));
      
      if (currentItem.zIndex < maxZIndex) {
        currentItem.zIndex += 1;
        setCanvasItems(updatedItems);
      }
    }
  };

  const handleSendBackward = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = [...canvasItems];
      const currentItem = updatedItems[selectedItemIndex];
      
      if (currentItem.zIndex > 1) {
        currentItem.zIndex -= 1;
        setCanvasItems(updatedItems);
      }
    }
  };

  // Delete selected item
  const handleDeleteItem = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = canvasItems.filter((_, index) => index !== selectedItemIndex);
      setCanvasItems(updatedItems);
      setSelectedItemIndex(null);
    }
  };

  // Capture canvas as image
  const captureView = async (canvas: React.RefObject<View>) => {
    if (!canvas.current) return null;
    try {
      setIsLoading(true);
      setSelectedItemIndex(null);

      const image = await makeImageFromView(canvas);
      const base64Image = image?.encodeToBase64();

      if (!base64Image) {
        console.error('Failed to encode image to base64');
        setIsLoading(false);
        return null;
      }

      return base64Image;
    } catch (error) {
      console.error('Error capturing view with Skia:', error);
      setIsLoading(false);
      throw error;
    }
  };

  // Handle successful image upload
  useEffect(() => {
    if (isImageUploaded && imageData?.fileURL) {
      handleSaveOutfit(imageData.fileURL);
    }
  }, [isImageUploaded, imageData]);

  // Save outfit to backend
  const handleSaveOutfit = async (collageImageUrl?: string) => {
    try {
      // Get selected item IDs from canvas (filter out undefined values)
      const selectedItemIds = canvasItems
        .map(item => item.itemId)
        .filter((id): id is string => Boolean(id));

      if (selectedItemIds.length === 0) {
        Alert.alert('No items selected', 'Please add at least one item to your outfit.');
        setIsLoading(false);
        return;
      }

      const outfitData = {
        name: outfitName || `Outfit ${new Date().toLocaleDateString()}`,
        location: {
          name: location,
          longitude: 0, // TODO: Add proper location coordinates
          latitude: 0,
        },
        eventDate: eventDate,
        itemIds: selectedItemIds,
        plannedDate: eventDate,
        ...(collageImageUrl && { collageImageUrl }), // Include collage image URL if available
      };

      if (editMode && existingOutfit) {
        // Update existing outfit
        updateOutfitMutation({
          outfitId: existingOutfit._id,
          ...outfitData,
        }, {
          onSuccess: () => {
            console.log('🎨 OutfitStyleDiaryModal - Outfit updated successfully');
            setIsLoading(false);
            setIsSuccessModalVisible(true);
          },
          onError: (error) => {
            console.error('🎨 OutfitStyleDiaryModal - Error updating outfit:', error);
            setIsLoading(false);
            Alert.alert('Error', 'Failed to update outfit. Please try again.');
          }
        });
      } else {
        // Create new outfit
        createOutfitMutation(outfitData, {
          onSuccess: () => {
            console.log('🎨 OutfitStyleDiaryModal - Outfit created successfully');
            setIsLoading(false);
            setIsSuccessModalVisible(true);
          },
          onError: (error) => {
            console.error('🎨 OutfitStyleDiaryModal - Error creating outfit:', error);
            setIsLoading(false);
            Alert.alert('Error', 'Failed to create outfit. Please try again.');
          }
        });
      }
    } catch (error) {
      console.error(`Error ${editMode ? 'updating' : 'creating'} outfit:`, error);
      Alert.alert(
        'Error',
        `Failed to ${editMode ? 'update' : 'create'} outfit. Please try again.`,
        [{ text: 'OK' }]
      );
      setIsLoading(false);
    }
  };

  // Handle save button press
  const handleSave = async () => {
    if (canvasItems.length === 0) {
      Alert.alert('No items', 'Please add at least one item to your outfit.');
      return;
    }

    try {
      // Capture the canvas as an image
      const base64Image = await captureView(canvasRef as React.RefObject<View>);

      if (base64Image) {
        // Upload the image to S3
        uploadImageMutation({
          fileName: `outfit-collage-${Date.now()}`,
          fileType: 'image/jpeg',
          folderPath: 'outfits',
          imageUrl: base64Image,
        });
      } else {
        // Save without collage image
        handleSaveOutfit();
      }
    } catch (error) {
      console.error('Error saving outfit:', error);
      Alert.alert('Error', 'Failed to save outfit. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.headerButton}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {editMode ? 'Edit Outfit' : 'Create Outfit'}
          </Text>
          <View style={{ width: 60 }} />
        </View>

        <ScrollView 
          style={styles.container} 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 150 }}
        >
          <LoadingOverlay visible={isLoading} />
          
          {/* Outfit Info */}
          <View style={styles.outfitInfo}>
            <Text style={styles.outfitTitle}>
              {editMode ? `Editing: ${existingOutfit?.name || 'Outfit'}` : 'New Outfit'}
            </Text>
          </View>

          {/* Item Selector */}
          <ItemSelector
            items={availableItems}
            onSelectItem={handleAddItem}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            categories={categories}
            handleNewItem={() => {
              // TODO: Implement add new item functionality
              console.log('Add new item pressed');
            }}
          />

          {/* Canvas */}
          <View ref={canvasRef} collapsable={false}>
            <Canvas showGrid={false}>
              {sortedCanvasItems.map((item, index) => (
                <DraggableResizeableItem
                  key={item.id}
                  source={item.source}
                  initialX={item.x}
                  initialY={item.y}
                  initialWidth={item.width}
                  initialHeight={item.height}
                  isSelected={selectedItemIndex === index}
                  onSelect={() => setSelectedItemIndex(index)}
                  onPositionChange={(position) => handleItemChange(index, position)}
                  onSizeChange={(changes) => handleItemChange(index, changes)}
                />
              ))}
            </Canvas>
          </View>

          {/* Z-Index Controls */}
          <ZIndexControls
            onBringForward={handleBringForward}
            onSendBackward={handleSendBackward}
            onDelete={handleDeleteItem}
            disabled={selectedItemIndex === null}
          />
        </ScrollView>

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <Button
            title={editMode ? "Update Outfit" : "Save Outfit"}
            onPress={() => {
              Alert.alert(
                'Save Outfit',
                'This will save the current outfit configuration',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Save', onPress: handleSave },
                ],
                { cancelable: true },
              );
            }}
            isDisabled={isLoading || canvasItems.length === 0}
          />
        </View>

        {/* Success Modal */}
        <Modal
          style={{ justifyContent: 'center', alignItems: 'center', margin: 20 }}
          isVisible={isSuccessModalVisible}
          onBackButtonPress={() => {
            setIsSuccessModalVisible(false);
            onClose();
          }}
          onBackdropPress={() => {
            setIsSuccessModalVisible(false);
            onClose();
          }}
        >
          <View style={styles.successModal}>
            <Text style={styles.successTitle}>
              {editMode ? 'Outfit Updated!' : 'Outfit Created!'}
            </Text>
            <Text style={styles.successMessage}>
              Your outfit has been {editMode ? 'updated' : 'saved'} successfully with {canvasItems.length} items.
            </Text>
            <TouchableOpacity
              style={styles.successButton}
              onPress={() => {
                setIsSuccessModalVisible(false);
                onClose();
              }}
            >
              <Text style={styles.successButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

export default OutfitStyleDiaryModal;

const styles = StyleSheet.create({
  modalContainer: {
    height: '100%',
    backgroundColor: '#F0F0F0',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 50,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerButton: {
    color: '#0E7E61',
    fontSize: 16,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  container: {
    flex: 1,
    backgroundColor: '#F0F0F0',
  },
  outfitInfo: {
    alignItems: 'center',
    padding: 15,
  },
  outfitTitle: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 18,
    color: '#333',
  },
  saveButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 50,
    backgroundColor: '#FFF',
    padding: 15,
  },
  successModal: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E7E61',
    marginBottom: 15,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 22,
  },
  successButton: {
    backgroundColor: '#0E7E61',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  successButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
